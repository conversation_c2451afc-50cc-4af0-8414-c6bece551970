.landing-section {
  width: 100%;
  max-width: var(--cMaxWidth);
  margin: auto;
  position: relative;
  height: var(--vh);
}
.landing-container {
  width: var(--cWidth);
  margin: auto;
  height: 100%;
  position: relative;
  max-width: var(--cMaxWidth);
}
.landing-circle1 {
  top: 0%;
  left: 0%;
  z-index: 15;
  position: fixed;
  width: 300px;
  height: 300px;
  background: linear-gradient(45deg, var(--crayonPink), var(--crayonOrange));
  box-shadow: inset -50px 40px 50px rgba(255, 107, 107, 0.4);
  filter: blur(60px);
  border-radius: 50%;
  animation: loadingCircle 5s linear infinite;
}
.nav-fade {
  position: fixed;
  top: 0;
  width: 100%;
  height: 130px;
  background-image: linear-gradient(
    0deg,
    transparent,
    var(--backgroundColor) 70%
  );
  pointer-events: none;
  z-index: 12;
  opacity: 0;
  left: 0;
}
@keyframes loadingCircle {
  0% {
    transform: translate(-95%, -75%) rotateZ(0deg);
  }
  100% {
    transform: translate(-95%, -75%) rotateZ(360deg);
  }
}

.landing-circle2 {
  top: 50%;
  right: 0%;
  transform: translate(calc(100% - 2px), -50%);
  z-index: 9;
  position: fixed;
  display: none;
  width: 300px;
  height: 300px;
  background-color: #fb8dff;
  box-shadow: inset -50px 40px 50px rgba(84, 0, 255, 0.6);
  filter: blur(50px);
  border-radius: 50%;
  animation: loadingCircle2 5s linear infinite;
}
@keyframes loadingCircle2 {
  100% {
    transform: translate(calc(100% - 2px), -50%) rotate(360deg);
  }
}
.landing-video,
.landing-image {
  position: absolute;
  bottom: 0;
  height: 95%;
  left: 50%;
  transform: translateX(-50%);
}
.landing-image img {
  height: 100%;
  z-index: 2;
  position: relative;
}
.character-rim {
  position: absolute;
  width: 400px;
  height: 400px;
  z-index: 1;
  background-color: #f59bf8;
  transform: translate(-50%, 36%) scaleX(1.4);
  box-shadow: inset 66px 35px 85px 0px rgba(85, 0, 255, 0.65);
  filter: blur(50px);
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, 100%) scale(1.4);
  opacity: 0;
}
.character-model {
  height: 80%;
  height: 80vh;
  position: absolute;
  max-width: 1920px;
  max-height: 1080px;
  transform: translateX(-50%);
  width: 100%;
  left: 50%;
  z-index: 0;
  bottom: 50px;
  pointer-events: inherit;
}
.character-model::after {
  content: "";
  width: 100vw;
  height: 250px;
  background-image: linear-gradient(
    to bottom,
    transparent,
    var(--backgroundColor) 70%
  );
  bottom: -50px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 9;
  position: absolute;
}
.character-model::before {
  content: "";
  width: 100vw;
  height: 700px;
  background-color: var(--backgroundColor);
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  z-index: 9;
  position: absolute;
}
.character-loaded .character-rim {
  animation: backlight 3s forwards;
  animation-delay: 0.3s;
  opacity: 0;
}
.character-model canvas {
  position: relative;
  pointer-events: none;
  z-index: 2;
}
.character-hover {
  position: absolute;
  width: 280px;
  height: 280px;
  top: 50%;
  left: 50%;
  z-index: 3;
  transform: translate(-50%, -50%);
  border-radius: 50%;
}
.landing-intro {
  position: absolute;
  z-index: 9;
  top: 12%;
  left: 0;
}
.landing-intro h2 {
  margin: 0;
  color: var(--accentColor);
  font-size: 22px;
  font-weight: 300;
  letter-spacing: 2px;
}

.landing-intro h1 {
  margin: 0;
  letter-spacing: 2px;
  font-size: 28px;
  line-height: 28px;
  font-weight: 500;
  font-family: "Geist", sans-serif;
}

/* .landing-intro h1 span {
   font-weight: 200; 
} */

.landing-info {
  position: absolute;
  right: 50%;
  transform: translateX(50%);
  bottom: 40px;
  top: inherit;
  z-index: 9;
}
.landing-info h3 {
  font-size: 22px;
  letter-spacing: 2px;
  font-weight: 300;
  color: var(--accentColor);
  margin: 0;
}
.landing-info h2 {
  margin: 0;
  margin-top: -20px;
  margin-left: 20px;
  font-family: "Geist", sans-serif;
  font-weight: 600;
  font-size: 32px;
  line-height: 40px;
  position: relative;
  display: flex;
  flex-wrap: nowrap;
  text-transform: uppercase;
  letter-spacing: 2px;
}
.landing-h2-info-1 {
  position: absolute;
  top: 0;
}
h2.landing-info-h2 {
  color: #c481ff;
  font-size: 42px;
  width: 120%;
  margin: 0;
  font-family: "Geist", sans-serif;
  font-weight: 600;
  position: relative;
  margin-left: -5px;
}
.landing-h2-2 {
  position: absolute;
  top: 0;
}
.landing-info-h2::after {
  content: "";
  position: absolute;
  width: 100%;
  height: 120%;
  z-index: 3;
  background-image: linear-gradient(
    0deg,
    var(--backgroundColor) 40%,
    rgba(0, 0, 0, 0) 110%
  );
  top: 0;
  left: 0;
}
@media screen and (min-width: 500px) {
  .landing-circle2 {
    display: block;
  }
  .character-model {
    z-index: 0;
  }
  .landing-info h3 {
    font-size: 18px;
  }
  .landing-intro h2 {
    font-size: 18px;
  }
  .landing-intro h1 {
    font-size: 30px;
    line-height: 30px;
  }
  .landing-info h2 {
    font-size: 35px;
    line-height: 40px;
  }
  h2.landing-info-h2 {
    font-size: 38px;
  }
}
@media screen and (min-width: 768px) {
  .character-model {
    height: 80vh;
  }
  .landing-intro h2 {
    font-size: 25px;
  }
  .landing-intro h1 {
    font-size: 40px;
    line-height: 35px;
  }
  .landing-info h3 {
    font-size: 25px;
  }
  .landing-info h2 {
    font-size: 45px;
    line-height: 42px;
  }
  h2.landing-info-h2 {
    font-size: 55px;
  }
}
@media screen and (min-width: 1025px) {
  .character-model {
    height: 100vh;
    bottom: 0;
    z-index: 11;
    position: fixed;
  }
  .character-model::after,
  .character-model::before {
    display: none;
  }
  .landing-intro {
    top: 50%;
    left: auto;
    right: 66%;
    transform: translate(0%, -50%);
  }

  .landing-info {
    bottom: auto;
    top: 51%;
    z-index: inherit;
    text-align: left;
    transform: translate(0%, -50%);
    right: auto;
    left: 66%;
  }
}
@media screen and (min-width: 1200px) {
  .landing-intro {
    top: 50%;
    left: auto;
    right: 70%;
    transform: translate(0%, -50%);
  }

  .landing-info {
    bottom: auto;
    top: 51%;
    z-index: inherit;
    text-align: left;
    transform: translate(0%, -50%);
    right: auto;
    left: 70%;
  }
}
@media screen and (min-width: 1600px) {
  .landing-intro h2 {
    font-size: 35px;
  }
  .landing-intro h1 {
    font-size: 60px;
    line-height: 55px;
  }
  .landing-info h3 {
    font-size: 35px;
  }
  .landing-info h2 {
    font-size: 65px;
    line-height: 62px;
  }
  h2.landing-info-h2 {
    font-size: 75px;
  }
}
