/**
 * Free alternative to GSAP SplitText plugin
 * Splits text into characters, words, or lines for animation
 */

export interface SplitTextOptions {
  type?: 'chars' | 'words' | 'lines' | string;
  linesClass?: string;
  wordsClass?: string;
  charsClass?: string;
}

export class SplitText {
  public chars: HTMLElement[] = [];
  public words: HTMLElement[] = [];
  public lines: HTMLElement[] = [];
  private originalHTML: string = '';
  private element: HTMLElement;

  constructor(element: HTMLElement | string, options: SplitTextOptions = {}) {
    this.element = typeof element === 'string' 
      ? document.querySelector(element) as HTMLElement 
      : element;
    
    if (!this.element) {
      throw new Error('Element not found');
    }

    this.originalHTML = this.element.innerHTML;
    this.split(options);
  }

  private split(options: SplitTextOptions) {
    const { type = 'chars', linesClass = 'split-line', wordsClass = 'split-word', charsClass = 'split-char' } = options;
    
    const text = this.element.textContent || '';
    this.element.innerHTML = '';

    if (type.includes('lines')) {
      this.splitIntoLines(text, linesClass);
    } else if (type.includes('words')) {
      this.splitIntoWords(text, wordsClass);
    } else if (type.includes('chars')) {
      this.splitIntoChars(text, charsClass);
    }

    // Handle multiple types (e.g., "chars,lines")
    if (type.includes(',')) {
      const types = type.split(',').map(t => t.trim());
      if (types.includes('chars') && types.includes('lines')) {
        this.splitIntoCharsAndLines(text, charsClass, linesClass);
      }
    }
  }

  private splitIntoChars(text: string, className: string) {
    const chars = text.split('');
    chars.forEach(char => {
      const span = document.createElement('span');
      span.className = className;
      span.textContent = char === ' ' ? '\u00A0' : char; // Non-breaking space
      span.style.display = 'inline-block';
      this.chars.push(span);
      this.element.appendChild(span);
    });
  }

  private splitIntoWords(text: string, className: string) {
    const words = text.split(' ');
    words.forEach((word, index) => {
      const span = document.createElement('span');
      span.className = className;
      span.textContent = word;
      span.style.display = 'inline-block';
      this.words.push(span);
      this.element.appendChild(span);
      
      // Add space after word (except last)
      if (index < words.length - 1) {
        this.element.appendChild(document.createTextNode(' '));
      }
    });
  }

  private splitIntoLines(text: string, className: string) {
    // Simple line splitting - in real implementation, you'd need to measure text
    const lines = text.split('\n');
    lines.forEach(line => {
      const div = document.createElement('div');
      div.className = className;
      div.textContent = line;
      div.style.overflow = 'hidden';
      this.lines.push(div);
      this.element.appendChild(div);
    });
  }

  private splitIntoCharsAndLines(text: string, charsClass: string, linesClass: string) {
    // Create line containers first
    const lines = text.split('\n');
    lines.forEach(lineText => {
      const lineDiv = document.createElement('div');
      lineDiv.className = linesClass;
      lineDiv.style.overflow = 'hidden';
      
      // Split each line into characters
      const chars = lineText.split('');
      chars.forEach(char => {
        const span = document.createElement('span');
        span.className = charsClass;
        span.textContent = char === ' ' ? '\u00A0' : char;
        span.style.display = 'inline-block';
        this.chars.push(span);
        lineDiv.appendChild(span);
      });
      
      this.lines.push(lineDiv);
      this.element.appendChild(lineDiv);
    });
  }

  public revert() {
    this.element.innerHTML = this.originalHTML;
    this.chars = [];
    this.words = [];
    this.lines = [];
  }
}

// Helper function for backward compatibility
export function createSplitText(element: HTMLElement | string, options?: SplitTextOptions) {
  return new SplitText(element, options);
}
