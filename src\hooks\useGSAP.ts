import { useEffect, useRef, DependencyList } from 'react';
import { gsap } from 'gsap';

/**
 * Custom hook untuk GSAP - pengganti @gsap/react
 * Menyediakan context yang aman untuk animasi GSAP dalam React
 */

export interface UseGSAPOptions {
  scope?: React.RefObject<HTMLElement>;
  dependencies?: DependencyList;
  revertOnUpdate?: boolean;
}

export function useGSAP(
  callback: () => void | (() => void),
  options: UseGSAPOptions = {}
) {
  const { scope, dependencies = [], revertOnUpdate = false } = options;
  const contextRef = useRef<gsap.Context | null>(null);
  const cleanupRef = useRef<(() => void) | null>(null);

  useEffect(() => {
    // Create GSAP context
    const ctx = gsap.context(() => {
      // Execute the callback and store cleanup function if returned
      const cleanup = callback();
      if (typeof cleanup === 'function') {
        cleanupRef.current = cleanup;
      }
    }, scope?.current || undefined);

    contextRef.current = ctx;

    // Cleanup function
    return () => {
      if (cleanupRef.current) {
        cleanupRef.current();
        cleanupRef.current = null;
      }
      ctx.revert();
      contextRef.current = null;
    };
  }, dependencies);

  // Revert on update if specified
  useEffect(() => {
    if (revertOnUpdate && contextRef.current) {
      contextRef.current.revert();
    }
  }, dependencies);

  return contextRef.current;
}

/**
 * Hook untuk membuat timeline GSAP
 */
export function useGSAPTimeline(options?: gsap.TimelineVars, dependencies: DependencyList = []) {
  const timelineRef = useRef<gsap.core.Timeline | null>(null);

  useEffect(() => {
    timelineRef.current = gsap.timeline(options);

    return () => {
      if (timelineRef.current) {
        timelineRef.current.kill();
        timelineRef.current = null;
      }
    };
  }, dependencies);

  return timelineRef.current;
}

/**
 * Hook untuk animasi GSAP dengan auto-cleanup
 */
export function useGSAPAnimation(
  target: string | object,
  vars: gsap.TweenVars,
  dependencies: DependencyList = []
) {
  const tweenRef = useRef<gsap.core.Tween | null>(null);

  useEffect(() => {
    tweenRef.current = gsap.to(target, vars);

    return () => {
      if (tweenRef.current) {
        tweenRef.current.kill();
        tweenRef.current = null;
      }
    };
  }, dependencies);

  return tweenRef.current;
}

/**
 * Hook untuk ScrollTrigger dengan auto-cleanup
 */
export function useScrollTrigger(
  config: ScrollTrigger.Vars,
  dependencies: DependencyList = []
) {
  const triggerRef = useRef<ScrollTrigger | null>(null);

  useEffect(() => {
    const { ScrollTrigger } = require('gsap/ScrollTrigger');
    gsap.registerPlugin(ScrollTrigger);
    
    triggerRef.current = ScrollTrigger.create(config);

    return () => {
      if (triggerRef.current) {
        triggerRef.current.kill();
        triggerRef.current = null;
      }
    };
  }, dependencies);

  return triggerRef.current;
}

/**
 * Hook untuk refresh ScrollTrigger
 */
export function useScrollTriggerRefresh(dependencies: DependencyList = []) {
  useEffect(() => {
    const { ScrollTrigger } = require('gsap/ScrollTrigger');
    ScrollTrigger.refresh();
  }, dependencies);
}

// Export default useGSAP untuk backward compatibility
export default useGSAP;
