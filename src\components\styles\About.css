.about-section {
  display: flex;
  align-items: center;
  justify-content: left;
  place-items: center;
  position: relative;
  opacity: 1;
  height: auto;
  width: var(--cWidth);
  margin: auto;
}
.about-me {
  padding: 50px 0px;
  padding-bottom: 0;
  width: 500px;
  max-width: calc(100% - 15px);
}
.about-me h3 {
  font-size: 25px;
  text-transform: uppercase;
  letter-spacing: 7px;
  font-weight: 400;
  background: linear-gradient(45deg, var(--crayonGreen), var(--crayonBlue));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
}

.about-me p {
  font-size: 33px;
  font-weight: 600;
  line-height: 36px;
  letter-spacing: 1px;
}

@media only screen and (min-width: 600px) {
  .about-section {
    justify-content: center;
  }
}
@media only screen and (min-width: 768px) {
  .about-me {
    width: 500px;
    max-width: calc(100% - 70px);
    transform: translateY(0%);
  }
  .about-section {
    opacity: 1;
  }
}
@media only screen and (min-width: 1025px) {
  .about-section {
    width: var(--cWidth);
    justify-content: right;
    max-width: 1920px;
    height: var(--vh);
    padding: 0px;
    opacity: 1;
  }
  .about-me {
    padding: 0px;
    width: 50%;
  }
  .about-me p {
    font-size: 1.9vw;
    line-height: 2.3vw;
  }
}
@media only screen and (min-width: 1950px) {
  .about-me p {
    font-size: 2.5rem;
    line-height: 2.7rem;
  }
}
