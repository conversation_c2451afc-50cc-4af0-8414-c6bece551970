{"name": "moncy-portfolio", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@gsap/react": "^2.1.1", "@react-three/cannon": "^6.6.0", "@react-three/drei": "^9.120.4", "@react-three/fiber": "^8.17.10", "@react-three/postprocessing": "^2.16.3", "@react-three/rapier": "^1.5.0", "@types/three": "^0.168.0", "@vercel/analytics": "^1.4.1", "gsap": "^3.12.7", "gsap-trial": "^3.12.7", "react": "^18.3.1", "react-dom": "^18.3.1", "react-fast-marquee": "^1.6.5", "react-icons": "^5.3.0", "three": "^0.168.0", "three-stdlib": "^2.33.0"}, "devDependencies": {"@eslint/js": "^9.9.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1"}}