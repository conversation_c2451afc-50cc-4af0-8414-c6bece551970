{"name": "moncy-portfolio", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@react-three/cannon": "^6.5.2", "@react-three/drei": "^9.88.7", "@react-three/fiber": "^8.15.11", "@react-three/postprocessing": "^2.15.1", "@react-three/rapier": "^1.1.2", "@types/three": "^0.158.2", "@vercel/analytics": "^1.1.1", "gsap": "^3.12.2", "lenis": "^1.0.29", "react": "^18.2.0", "react-dom": "^18.2.0", "react-fast-marquee": "^1.6.2", "react-icons": "^4.11.0", "three": "^0.158.0", "three-stdlib": "^2.28.5"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "typescript": "^5.0.2", "vite": "^4.4.5"}}