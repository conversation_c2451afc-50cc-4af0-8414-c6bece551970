import Lenis from 'lenis';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

export class SmoothScroll {
  private lenis: Lenis | null = null;

  constructor() {
    this.init();
  }

  private init() {
    // Initialize Lenis smooth scroll
    this.lenis = new Lenis({
      duration: 1.2,
      easing: (t: number) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),
      direction: 'vertical',
      gestureDirection: 'vertical',
      smooth: true,
      mouseMultiplier: 1,
      smoothTouch: false,
      touchMultiplier: 2,
      infinite: false,
    });

    // Connect Lenis with GSAP ScrollTrigger
    this.lenis.on('scroll', ScrollTrigger.update);

    gsap.ticker.add((time) => {
      this.lenis?.raf(time * 1000);
    });

    gsap.ticker.lagSmoothing(0);
  }

  public scrollTo(target: string | number, options?: any) {
    this.lenis?.scrollTo(target, options);
  }

  public start() {
    this.lenis?.start();
  }

  public stop() {
    this.lenis?.stop();
  }

  public destroy() {
    this.lenis?.destroy();
    this.lenis = null;
  }

  public paused(value?: boolean) {
    if (value !== undefined) {
      if (value) {
        this.stop();
      } else {
        this.start();
      }
    }
    return !this.lenis?.isScrolling;
  }
}

// Export singleton instance
export const smoothScroll = new SmoothScroll();
export let smoother = smoothScroll; // For backward compatibility
